/**
 * ESP-Ducky Bluetooth Management Implementation
 * 
 * Author: <PERSON> - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#include "bluetooth_manager.h"

// Global instance
BluetoothManager bluetoothManager;

BluetoothManager::BluetoothManager() {
    blecky = nullptr;
    currentState = BT_DISABLED;
    previousState = BT_DISABLED;
    initialized = false;
    enabled = false;
    deviceName = DEFAULT_BLE_NAME;
    manufacturer = BLE_MANUFACTURER;
    isConnected = false;
    connectionTime = 0;
    lastActivity = 0;
    connectionCount = 0;
    executionStatus = EXEC_IDLE;
    executedCommands = 0;
    totalCommands = 0;
    totalExecutionTime = 0;
    successfulExecutions = 0;
    failedExecutions = 0;
}

BluetoothManager::~BluetoothManager() {
    if (blecky) {
        delete blecky;
    }
}

bool BluetoothManager::begin() {
    DEBUG_PRINTLN("[Bluetooth] Initializing Bluetooth manager...");
    
    // Create Blecky instance
    blecky = new Blecky(deviceName.c_str(), manufacturer.c_str(), 100);
    
    if (!blecky) {
        DEBUG_PRINTLN("[Bluetooth] ERROR: Failed to create Blecky instance");
        return false;
    }
    
    // Enable debug logging
    blecky->setDebug(true);
    
    // Initialize Blecky
    blecky->begin(false);  // Don't wait for connection during init
    if (!blecky) {
        DEBUG_PRINTLN("[Bluetooth] ERROR: Failed to initialize Blecky");
        delete blecky;
        blecky = nullptr;
        return false;
    }
    
    currentState = BT_ADVERTISING;
    enabled = true;
    initialized = true;
    
    DEBUG_PRINTLN("[Bluetooth] Bluetooth manager initialized successfully");
    DEBUG_PRINTLN("[Bluetooth] Device name: " + deviceName);
    DEBUG_PRINTLN("[Bluetooth] Manufacturer: " + manufacturer);
    DEBUG_PRINTLN("[Bluetooth] Status: Advertising and ready for connections");
    
    return true;
}

void BluetoothManager::end() {
    if (blecky) {
        delete blecky;
        blecky = nullptr;
    }
    
    currentState = BT_DISABLED;
    enabled = false;
    initialized = false;
    isConnected = false;
    
    DEBUG_PRINTLN("[Bluetooth] Bluetooth manager stopped");
}

bool BluetoothManager::enable() {
    if (!initialized) {
        return begin();
    }
    
    if (enabled) {
        DEBUG_PRINTLN("[Bluetooth] Bluetooth already enabled");
        return true;
    }
    
    // Re-enable Bluetooth
    if (blecky) {
        blecky->begin(false);
        enabled = true;
        currentState = BT_ADVERTISING;
        DEBUG_PRINTLN("[Bluetooth] Bluetooth enabled");
        return true;
    }
    
    DEBUG_PRINTLN("[Bluetooth] ERROR: Failed to enable Bluetooth");
    return false;
}

bool BluetoothManager::disable() {
    if (!enabled) {
        DEBUG_PRINTLN("[Bluetooth] Bluetooth already disabled");
        return true;
    }
    
    // Stop Bluetooth
    if (blecky) {
        // Blecky doesn't have a direct stop method, so we'll mark as disabled
        enabled = false;
        currentState = BT_DISABLED;
        isConnected = false;
        
        DEBUG_PRINTLN("[Bluetooth] Bluetooth disabled");
        return true;
    }
    
    return false;
}

void BluetoothManager::setDeviceName(String name) {
    deviceName = name;
    
    if (blecky) {
        // Would need to restart Bluetooth to apply new name
        DEBUG_PRINTLN("[Bluetooth] Device name updated: " + name);
        DEBUG_PRINTLN("[Bluetooth] Note: Restart required to apply changes");
    }
}

void BluetoothManager::update() {
    if (!enabled || !blecky) return;
    
    // Update connection state
    updateConnectionState();
    
    // Handle state changes
    if (currentState != previousState) {
        handleStateChange();
        previousState = currentState;
    }
    
    // Update activity timestamp
    lastActivity = millis();
}

void BluetoothManager::updateConnectionState() {
    if (!blecky) return;
    
    // Check if connected (simplified - Blecky doesn't expose connection state directly)
    bool wasConnected = isConnected;
    isConnected = blecky->isConnected();
    
    if (isConnected && !wasConnected) {
        // Just connected
        currentState = BT_CONNECTED;
        connectionTime = millis();
        connectionCount++;
        onConnectionChanged(true);
    } else if (!isConnected && wasConnected) {
        // Just disconnected
        currentState = BT_ADVERTISING;
        onConnectionChanged(false);
    }
}

void BluetoothManager::onConnectionChanged(bool connected) {
    if (connected) {
        DEBUG_PRINTLN("[Bluetooth] Device connected");
        DEBUG_PRINTF("[Bluetooth] Total connections: %d\n", connectionCount);
    } else {
        DEBUG_PRINTLN("[Bluetooth] Device disconnected");
        DEBUG_PRINTF("[Bluetooth] Connection duration: %lu ms\n", millis() - connectionTime);
    }
}

void BluetoothManager::handleStateChange() {
    DEBUG_PRINTF("[Bluetooth] State changed: %s -> %s\n", 
                stateToString(previousState).c_str(), 
                stateToString(currentState).c_str());
}

bool BluetoothManager::executePayload(String payload) {
    if (!enabled || !blecky) {
        DEBUG_PRINTLN("[Bluetooth] ERROR: Bluetooth not enabled");
        lastError = "Bluetooth not enabled";
        return false;
    }
    
    if (!isConnected) {
        DEBUG_PRINTLN("[Bluetooth] ERROR: No device connected");
        lastError = "No device connected";
        return false;
    }
    
    DEBUG_PRINTLN("[Bluetooth] Executing payload...");
    executionStatus = EXEC_RUNNING;
    currentPayload = payload;
    
    unsigned long startTime = millis();
    
    // Set payload and execute
    if (blecky->setPayloadFromString(payload)) {
        if (blecky->run()) {
            executionStatus = EXEC_COMPLETED;
            successfulExecutions++;
            totalExecutionTime += millis() - startTime;
            
            DEBUG_PRINTF("[Bluetooth] Payload executed successfully in %lu ms\n", millis() - startTime);
            return true;
        } else {
            executionStatus = EXEC_ERROR;
            failedExecutions++;
            lastError = "Payload execution failed";
            
            DEBUG_PRINTLN("[Bluetooth] ERROR: Payload execution failed");
            return false;
        }
    } else {
        executionStatus = EXEC_ERROR;
        failedExecutions++;
        lastError = "Failed to set payload";
        
        DEBUG_PRINTLN("[Bluetooth] ERROR: Failed to set payload");
        return false;
    }
}

bool BluetoothManager::sendCommand(String command) {
    if (!enabled || !blecky || !isConnected) {
        return false;
    }

    blecky->executeCommand(command);
    return true;
}

bool BluetoothManager::sendKeyPress(String keys) {
    return sendCommand("PRESS " + keys);
}

bool BluetoothManager::sendString(String text) {
    return sendCommand("STRING " + text);
}

bool BluetoothManager::sendDelay(int milliseconds) {
    return sendCommand("DELAY " + String(milliseconds));
}

float BluetoothManager::getExecutionProgress() {
    if (totalCommands == 0) return 0.0;
    return (float)executedCommands / totalCommands * 100.0;
}

float BluetoothManager::getSuccessRate() {
    int totalExecutions = successfulExecutions + failedExecutions;
    if (totalExecutions == 0) return 0.0;
    return (float)successfulExecutions / totalExecutions * 100.0;
}

String BluetoothManager::stateToString(BluetoothState state) {
    switch (state) {
        case BT_DISABLED: return "DISABLED";
        case BT_INITIALIZING: return "INITIALIZING";
        case BT_ADVERTISING: return "ADVERTISING";
        case BT_CONNECTED: return "CONNECTED";
        case BT_DISCONNECTED: return "DISCONNECTED";
        case BT_ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

String BluetoothManager::executionStatusToString(ExecutionStatus status) {
    switch (status) {
        case EXEC_IDLE: return "IDLE";
        case EXEC_RUNNING: return "RUNNING";
        case EXEC_COMPLETED: return "COMPLETED";
        case EXEC_ERROR: return "ERROR";
        case EXEC_CANCELLED: return "CANCELLED";
        default: return "UNKNOWN";
    }
}

void BluetoothManager::printStatus() {
    DEBUG_PRINTLN("=== Bluetooth Status ===");
    DEBUG_PRINTLN("State: " + stateToString(currentState));
    DEBUG_PRINTLN("Enabled: " + String(enabled ? "Yes" : "No"));
    DEBUG_PRINTLN("Connected: " + String(isConnected ? "Yes" : "No"));
    DEBUG_PRINTLN("Device Name: " + deviceName);
    DEBUG_PRINTLN("Manufacturer: " + manufacturer);
    DEBUG_PRINTF("Connection Count: %d\n", connectionCount);
    DEBUG_PRINTF("Last Activity: %lu ms ago\n", millis() - lastActivity);
    DEBUG_PRINTLN("Execution Status: " + executionStatusToString(executionStatus));
    if (!lastError.isEmpty()) {
        DEBUG_PRINTLN("Last Error: " + lastError);
    }
    DEBUG_PRINTLN("========================");
}

void BluetoothManager::printStatistics() {
    DEBUG_PRINTLN("=== Bluetooth Statistics ===");
    DEBUG_PRINTF("Total Connections: %d\n", connectionCount);
    DEBUG_PRINTF("Successful Executions: %d\n", successfulExecutions);
    DEBUG_PRINTF("Failed Executions: %d\n", failedExecutions);
    DEBUG_PRINTF("Success Rate: %.1f%%\n", getSuccessRate());
    DEBUG_PRINTF("Total Execution Time: %lu ms\n", totalExecutionTime);
    if (successfulExecutions > 0) {
        DEBUG_PRINTF("Average Execution Time: %lu ms\n", totalExecutionTime / successfulExecutions);
    }
    DEBUG_PRINTLN("============================");
}

void BluetoothManager::resetStatistics() {
    connectionCount = 0;
    successfulExecutions = 0;
    failedExecutions = 0;
    totalExecutionTime = 0;
    lastError = "";
    
    DEBUG_PRINTLN("[Bluetooth] Statistics reset");
}
