/**
 * ESP-Ducky Main Application
 * 
 * Bluetooth Rubber Ducky for Ethical Hackers and Cybersecurity Learners
 * Author: <PERSON>han - SKR Electronics Lab
 * Website: www.skrelectronicslab.com
 */

#include <Arduino.h>
#include <SPIFFS.h>
#include "config.h"
#include "display.h"
#include "buttons.h"
#include "payload_manager.h"
#include "bluetooth_manager.h"
#include "web_server.h"

// System state
bool systemInitialized = false;
unsigned long splashStartTime = 0;
unsigned long lastStatusUpdate = 0;

// Function declarations
void initializeSystem();
void handleSplashScreen();
void handleMainLoop();
void handleButtonEvents();
void handleMenuNavigation();
void executeSelectedPayload();
void deleteSelectedPayload();
void startAPMode();
void stopAPMode();
void updateSystemStatus();
void printSystemInfo();
void handleSerialCommands();

void setup() {
    // Initialize serial communication
    Serial.begin(DEBUG_BAUD_RATE);
    delay(1000);
    
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("==================================================");
    DEBUG_PRINTLN("ESP-Ducky Starting...");
    DEBUG_PRINTLN("Author: SK Raihan - SKR Electronics Lab");
    DEBUG_PRINTLN("Website: www.skrelectronicslab.com");
    DEBUG_PRINTLN("Version: " + String(FIRMWARE_VERSION));
    DEBUG_PRINTLN("==================================================");
    
    // Initialize system
    initializeSystem();
    
    DEBUG_PRINTLN("[System] ESP-Ducky initialized successfully");
    DEBUG_PRINTLN("[System] Ready for operation");
}

void loop() {
    // Handle splash screen timing
    if (displayManager.getCurrentMenu() == MENU_SPLASH) {
        handleSplashScreen();
        return;
    }
    
    // Main application loop
    handleMainLoop();
}

void initializeSystem() {
    DEBUG_PRINTLN("[System] Initializing ESP-Ducky...");
    
    // Initialize SPIFFS
    DEBUG_PRINT("[System] Initializing SPIFFS... ");
    if (!SPIFFS.begin(SPIFFS_FORMAT_ON_FAIL)) {
        DEBUG_PRINTLN("FAILED");
        DEBUG_PRINTLN("[System] CRITICAL: SPIFFS initialization failed");
        return;
    }
    DEBUG_PRINTLN("OK");
    
    // Initialize display
    DEBUG_PRINT("[System] Initializing display... ");
    if (!displayManager.begin()) {
        DEBUG_PRINTLN("FAILED");
        DEBUG_PRINTLN("[System] CRITICAL: Display initialization failed");
        return;
    }
    DEBUG_PRINTLN("OK");
    
    // Initialize buttons
    DEBUG_PRINT("[System] Initializing buttons... ");
    buttonManager.begin();
    DEBUG_PRINTLN("OK");
    
    // Initialize payload manager
    DEBUG_PRINT("[System] Initializing payload manager... ");
    if (!payloadManager.begin()) {
        DEBUG_PRINTLN("FAILED");
        DEBUG_PRINTLN("[System] WARNING: Payload manager initialization failed");
    } else {
        DEBUG_PRINTLN("OK");
    }
    
    // Initialize Bluetooth
    DEBUG_PRINT("[System] Initializing Bluetooth... ");
    if (!bluetoothManager.begin()) {
        DEBUG_PRINTLN("FAILED");
        DEBUG_PRINTLN("[System] WARNING: Bluetooth initialization failed");
    } else {
        DEBUG_PRINTLN("OK");
    }
    
    // Initialize web server (but don't start it)
    DEBUG_PRINT("[System] Initializing web server... ");
    if (!webServerManager.begin()) {
        DEBUG_PRINTLN("FAILED");
        DEBUG_PRINTLN("[System] WARNING: Web server initialization failed");
    } else {
        DEBUG_PRINTLN("OK");
    }
    
    // Set splash screen start time
    splashStartTime = millis();
    
    // Print system information
    printSystemInfo();
    
    systemInitialized = true;
}

void handleSplashScreen() {
    // Update display
    displayManager.update();
    
    // Check if splash screen duration has elapsed
    if (millis() - splashStartTime >= SPLASH_SCREEN_DURATION) {
        displayManager.setMenu(MENU_MAIN);
        DEBUG_PRINTLN("[System] Splash screen completed, entering main menu");
    }
    
    // Allow early exit with any button press
    buttonManager.update();
    if (buttonManager.hasEvent()) {
        ButtonEvent event = buttonManager.getEvent();
        if (event.state == BUTTON_PRESSED) {
            displayManager.setMenu(MENU_MAIN);
            DEBUG_PRINTLN("[System] Splash screen skipped by user");
        }
    }
}

void handleMainLoop() {
    // Update all managers
    buttonManager.update();
    bluetoothManager.update();
    webServerManager.update();

    // Handle button events
    handleButtonEvents();

    // Handle serial commands for testing
    handleSerialCommands();

    // Update system status periodically
    if (millis() - lastStatusUpdate >= STATUS_UPDATE_INTERVAL) {
        updateSystemStatus();
        lastStatusUpdate = millis();
    }

    // Update display
    displayManager.update();

    // Small delay to prevent excessive CPU usage
    delay(10);
}

void handleButtonEvents() {
    while (buttonManager.hasEvent()) {
        ButtonEvent event = buttonManager.getEvent();
        
        if (event.state == BUTTON_PRESSED) {
            switch (event.button) {
                case BUTTON_UP:
                    displayManager.navigateUp();
                    DEBUG_PRINTLN("[UI] Navigate up");
                    break;
                    
                case BUTTON_DOWN:
                    displayManager.navigateDown();
                    DEBUG_PRINTLN("[UI] Navigate down");
                    break;
                    
                case BUTTON_OK:
                    handleMenuNavigation();
                    break;
            }
        } else if (event.state == BUTTON_LONG_PRESS) {
            if (event.button == BUTTON_OK) {
                // Long press OK = go back
                displayManager.goBack();
                DEBUG_PRINTLN("[UI] Long press - go back");
            }
        }
    }
}

void handleMenuNavigation() {
    MenuState currentMenu = displayManager.getCurrentMenu();
    int selectedIndex = displayManager.getSelectedIndex();

    DEBUG_PRINTF("[UI] Menu select: %d in menu %d\n", selectedIndex, currentMenu);

    switch (currentMenu) {
        case MENU_MAIN:
            if (selectedIndex == 0) {
                // Payloads menu
                std::vector<String> payloads = payloadManager.getPayloadNames();
                displayManager.updatePayloadsMenu(payloads);
                displayManager.setMenu(MENU_PAYLOADS);
            } else if (selectedIndex == 1) {
                // Manage Payloads menu
                displayManager.setMenu(MENU_MANAGE_PAYLOADS);
            } else if (selectedIndex == 2) {
                // About menu
                displayManager.setMenu(MENU_ABOUT);
            }
            break;

        case MENU_PAYLOADS:
            executeSelectedPayload();
            break;

        case MENU_MANAGE_PAYLOADS:
            if (selectedIndex == 0) {
                // Delete Payloads
                std::vector<String> payloads = payloadManager.getPayloadNames();
                displayManager.updateDeletePayloadsMenu(payloads);
                displayManager.setMenu(MENU_DELETE_PAYLOADS);
            } else if (selectedIndex == 1) {
                // Add Payloads (AP Mode)
                startAPMode();
            }
            break;

        case MENU_DELETE_PAYLOADS:
            deleteSelectedPayload();
            break;

        case MENU_ADD_PAYLOADS:
            startAPMode();
            break;

        case MENU_ABOUT:
            displayManager.goBack();
            break;

        case MENU_AP_MODE:
            // Stop AP mode
            stopAPMode();
            break;

        default:
            displayManager.selectItem();
            break;
    }
}

void executeSelectedPayload() {
    int selectedIndex = displayManager.getSelectedIndex();
    std::vector<String> payloads = payloadManager.getPayloadNames();
    
    if (selectedIndex >= 0 && selectedIndex < payloads.size()) {
        String payloadName = payloads[selectedIndex];
        
        DEBUG_PRINTLN("[Payload] Executing: " + payloadName);
        displayManager.showMessage("Executing...", 1000);
        
        // Check Bluetooth connection
        if (!bluetoothManager.getConnectionStatus()) {
            displayManager.showMessage("Bluetooth not connected!", 2000);
            DEBUG_PRINTLN("[Payload] ERROR: Bluetooth not connected");
            return;
        }
        
        // Execute payload
        PayloadResult result = payloadManager.executePayload(selectedIndex);
        
        if (result.success) {
            displayManager.showMessage("Payload executed!", 2000);
            DEBUG_PRINTF("[Payload] Success: %d commands in %lu ms\n", 
                        result.commandsExecuted, result.executionTime);
        } else {
            displayManager.showMessage("Execution failed!", 2000);
            DEBUG_PRINTLN("[Payload] ERROR: " + result.message);
        }
    } else if (selectedIndex == payloads.size()) {
        // Back option selected
        displayManager.goBack();
    }
}

void deleteSelectedPayload() {
    int selectedIndex = displayManager.getSelectedIndex();
    std::vector<String> payloads = payloadManager.getPayloadNames();

    if (selectedIndex >= 0 && selectedIndex < payloads.size()) {
        String payloadName = payloads[selectedIndex];

        DEBUG_PRINTLN("[Payload] Delete requested: " + payloadName);

        // Show confirmation dialog
        displayManager.showConfirmDialog("Delete " + payloadName + "?", "Yes", "No");

        // Wait for user input (simplified - in real implementation would use button events)
        delay(2000);

        // For demonstration, assume "Yes" was selected
        if (payloadManager.deletePayload(selectedIndex)) {
            displayManager.showMessage("Payload deleted!", 1500);
            DEBUG_PRINTLN("[Payload] Successfully deleted: " + payloadName);

            // Refresh the delete menu
            std::vector<String> updatedPayloads = payloadManager.getPayloadNames();
            displayManager.updateDeletePayloadsMenu(updatedPayloads);
        } else {
            displayManager.showMessage("Delete failed!", 1500);
            DEBUG_PRINTLN("[Payload] ERROR: Failed to delete: " + payloadName);
        }

    } else if (selectedIndex == payloads.size()) {
        // Back option selected
        displayManager.goBack();
    }
}

void startAPMode() {
    DEBUG_PRINTLN("[AP] Starting Access Point mode...");
    
    // Disable Bluetooth first
    bluetoothManager.disable();
    
    // Start AP
    if (webServerManager.startAP()) {
        displayManager.setMenu(MENU_AP_MODE);
        displayManager.setWiFiAPStatus(true);
        DEBUG_PRINTLN("[AP] Access Point started successfully");
        DEBUG_PRINTLN("[AP] SSID: " + webServerManager.getAPSSID());
        DEBUG_PRINTLN("[AP] Password: " + webServerManager.getAPPassword());
        DEBUG_PRINTLN("[AP] IP: " + webServerManager.getAPIP());
    } else {
        displayManager.showMessage("AP start failed!", 2000);
        DEBUG_PRINTLN("[AP] ERROR: Failed to start Access Point");
    }
}

void stopAPMode() {
    DEBUG_PRINTLN("[AP] Stopping Access Point mode...");
    
    webServerManager.stopAP();
    displayManager.setWiFiAPStatus(false);
    
    // Re-enable Bluetooth
    bluetoothManager.enable();
    
    displayManager.setMenu(MENU_MANAGE_PAYLOADS);
    DEBUG_PRINTLN("[AP] Access Point stopped");
}

void updateSystemStatus() {
    // Update Bluetooth status
    displayManager.setBluetoothStatus(bluetoothManager.getConnectionStatus());
    
    // Update WiFi AP status
    displayManager.setWiFiAPStatus(webServerManager.isAPActive());
    
    // Update payload list if in payloads menu
    if (displayManager.getCurrentMenu() == MENU_PAYLOADS) {
        std::vector<String> payloads = payloadManager.getPayloadNames();
        displayManager.updatePayloadsMenu(payloads);
    }
    
    // Update delete payloads list if in delete menu
    if (displayManager.getCurrentMenu() == MENU_DELETE_PAYLOADS) {
        std::vector<String> payloads = payloadManager.getPayloadNames();
        displayManager.updateDeletePayloadsMenu(payloads);
    }
}

void printSystemInfo() {
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("=== ESP-Ducky System Information ===");
    DEBUG_PRINTLN("Device: " + String(DEVICE_NAME));
    DEBUG_PRINTLN("Version: " + String(FIRMWARE_VERSION));
    DEBUG_PRINTLN("Author: " + String(AUTHOR));
    DEBUG_PRINTLN("Organization: " + String(ORGANIZATION));
    DEBUG_PRINTLN("Website: " + String(WEBSITE));
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("Hardware Configuration:");
    DEBUG_PRINTLN("- Display: " + String(DISPLAY_NAME));
    DEBUG_PRINTLN("- Screen: " + String(SCREEN_WIDTH) + "x" + String(SCREEN_HEIGHT));
    DEBUG_PRINTLN("- Buttons: UP=" + String(BUTTON_UP_PIN) + ", DOWN=" + String(BUTTON_DOWN_PIN) + ", OK=" + String(BUTTON_OK_PIN));
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("Memory Information:");
    DEBUG_PRINTLN("- Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    DEBUG_PRINTLN("- SPIFFS Total: " + String(SPIFFS.totalBytes()) + " bytes");
    DEBUG_PRINTLN("- SPIFFS Used: " + String(SPIFFS.usedBytes()) + " bytes");
    DEBUG_PRINTLN("- SPIFFS Free: " + String(SPIFFS.totalBytes() - SPIFFS.usedBytes()) + " bytes");
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("Payload Information:");
    DEBUG_PRINTLN("- Available Payloads: " + String(payloadManager.getPayloadCount()));
    DEBUG_PRINTLN("- Default Payloads: " + String(payloadManager.getDefaultPayloadCount()));
    DEBUG_PRINTLN("- Custom Payloads: " + String(payloadManager.getCustomPayloadCount()));
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("=== System Ready ===");
    DEBUG_PRINTLN();
    DEBUG_PRINTLN("Serial Commands Available:");
    DEBUG_PRINTLN("- EXEC 0: Execute payload 0 (RickRoll)");
    DEBUG_PRINTLN("- EXEC 1: Execute payload 1 (CMD Hack)");
    DEBUG_PRINTLN("- LIST: List all payloads");
    DEBUG_PRINTLN("- STATUS: Show system status");
    DEBUG_PRINTLN("- HELP: Show this help");
    DEBUG_PRINTLN();
}

void handleSerialCommands() {
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        command.toUpperCase();

        DEBUG_PRINTLN("[Serial] Command received: " + command);

        if (command.startsWith("EXEC ")) {
            int payloadIndex = command.substring(5).toInt();
            DEBUG_PRINTF("[Serial] Executing payload %d\n", payloadIndex);

            if (!bluetoothManager.getConnectionStatus()) {
                DEBUG_PRINTLN("[Serial] ERROR: Bluetooth not connected!");
                return;
            }

            PayloadResult result = payloadManager.executePayload(payloadIndex);
            if (result.success) {
                DEBUG_PRINTF("[Serial] SUCCESS: Payload executed in %lu ms\n", result.executionTime);
            } else {
                DEBUG_PRINTLN("[Serial] ERROR: " + result.message);
            }
        }
        else if (command == "LIST") {
            DEBUG_PRINTLN("[Serial] Available payloads:");
            std::vector<String> payloads = payloadManager.getPayloadNames();
            for (int i = 0; i < payloads.size(); i++) {
                DEBUG_PRINTF("[Serial] %d: %s\n", i, payloads[i].c_str());
            }
        }
        else if (command == "STATUS") {
            DEBUG_PRINTLN("[Serial] System Status:");
            DEBUG_PRINTF("[Serial] Bluetooth: %s\n", bluetoothManager.getConnectionStatus() ? "Connected" : "Disconnected");
            DEBUG_PRINTF("[Serial] WiFi AP: %s\n", webServerManager.isAPActive() ? "Active" : "Inactive");
            DEBUG_PRINTF("[Serial] Payloads: %d\n", payloadManager.getPayloadCount());
            DEBUG_PRINTF("[Serial] Free Heap: %d bytes\n", ESP.getFreeHeap());
        }
        else if (command == "HELP") {
            DEBUG_PRINTLN("[Serial] Available Commands:");
            DEBUG_PRINTLN("[Serial] - EXEC <index>: Execute payload by index");
            DEBUG_PRINTLN("[Serial] - LIST: List all payloads");
            DEBUG_PRINTLN("[Serial] - STATUS: Show system status");
            DEBUG_PRINTLN("[Serial] - HELP: Show this help");
        }
        else {
            DEBUG_PRINTLN("[Serial] Unknown command. Type HELP for available commands.");
        }
    }
}
